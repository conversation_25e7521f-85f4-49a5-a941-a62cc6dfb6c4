# Exemplo de Uso - FTP Web Explorer

## 🎯 Guia Prático de Uso

### 1. Iniciando a Aplicação

```bash
# Instalar dependências (primeira vez)
npm install

# Iniciar o servidor
npm start
```

A aplicação estará disponível em: `http://localhost:3000`

### 2. Conectando a um Servidor FTP

#### Exemplo de Conexão
```
Servidor: ftp.exemplo.com
Porta: 21
Usuário: meuusuario
Senha: minhasenha
```

#### Servidores FTP Públicos para Teste
- **ftp.dlptest.com**
  - Usuário: dlpuser
  - Senha: rNrKYTX9g7z3RgJRmxWuGHbeu
  - Porta: 21

### 3. Funcionalidades Disponíveis

#### 📁 Navegação
- **Duplo clique** em pastas para entrar
- **Breadcrumb** para navegação rápida
- **Botão voltar** para histórico
- **Botão subir** para pasta pai

#### 📤 Upload de Arquivos
1. Clique no botão "Upload"
2. Arraste arquivos para a área ou clique para selecionar
3. Acompanhe o progresso
4. Arquivos aparecerão automaticamente na lista

#### 📥 Download de Arquivos
- Clique no ícone de download ao lado do arquivo
- O download iniciará automaticamente

#### 📂 Gerenciamento de Pastas
- **Criar**: Botão "Nova Pasta" → Digite o nome
- **Renomear**: Ícone de edição → Digite novo nome
- **Excluir**: Ícone de lixeira → Confirme a ação

#### 📄 Gerenciamento de Arquivos
- **Renomear**: Ícone de edição → Digite novo nome
- **Excluir**: Ícone de lixeira → Confirme a ação

### 4. Tipos de Arquivo Suportados

A aplicação reconhece e exibe ícones específicos para:

- **Documentos**: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX
- **Imagens**: JPG, JPEG, PNG, GIF
- **Vídeos**: MP4, AVI
- **Áudios**: MP3, WAV
- **Arquivos**: ZIP, RAR
- **Código**: HTML, CSS, JS, PHP, PY
- **Texto**: TXT

### 5. Recursos de Interface

#### 🎨 Design Responsivo
- Funciona em desktop, tablet e mobile
- Interface adaptativa
- Gradientes modernos

#### 🔔 Notificações
- Feedback em tempo real
- Notificações de sucesso/erro
- Auto-dismiss após 5 segundos

#### ⚡ Funcionalidades Avançadas
- Drag & drop para upload
- Barra de progresso
- Modais de confirmação
- Histórico de navegação

### 6. Solução de Problemas Comuns

#### ❌ Erro de Conexão
```
Possíveis causas:
- Credenciais incorretas
- Servidor inacessível
- Porta bloqueada
- Firewall ativo
```

#### 📤 Upload Falha
```
Verificar:
- Tamanho do arquivo (máx 100MB)
- Permissões de escrita
- Conexão estável
- Espaço disponível no servidor
```

#### 🔄 Interface Não Responde
```
Soluções:
- Atualizar a página (F5)
- Verificar console do navegador
- Reiniciar o servidor
- Limpar cache do navegador
```

### 7. Comandos Úteis

```bash
# Desenvolvimento com auto-reload
npm run dev

# Verificar dependências
npm list

# Atualizar dependências
npm update

# Verificar vulnerabilidades
npm audit
```

### 8. Estrutura de Arquivos

```
FTPWeb/
├── index.html          # Interface principal
├── styles.css          # Estilos visuais
├── script.js           # Lógica frontend
├── server.js           # Servidor backend
├── package.json        # Configurações npm
├── README.md           # Documentação
├── exemplo-uso.md      # Este arquivo
└── uploads/            # Arquivos temporários
```

### 9. Personalização

#### 🎨 Modificar Cores
Edite as variáveis CSS em `styles.css`:
```css
/* Cores principais */
--primary-color: #667eea;
--success-color: #38a169;
--danger-color: #e53e3e;
```

#### ⚙️ Configurar Limites
Modifique em `server.js`:
```javascript
// Tamanho máximo de upload
limits: {
    fileSize: 100 * 1024 * 1024 // 100MB
}
```

### 10. Segurança

#### 🔒 Boas Práticas
- Use conexões FTPS quando possível
- Não compartilhe credenciais
- Mantenha dependências atualizadas
- Configure timeouts apropriados

#### 🛡️ Validações Implementadas
- Sanitização de entrada
- Validação de tipos de arquivo
- Limpeza de arquivos temporários
- Tratamento de erros

---

## 🚀 Próximos Passos

1. **Teste** todas as funcionalidades
2. **Configure** seu servidor FTP
3. **Personalize** a interface se necessário
4. **Implante** em produção se desejado

**Aproveite sua nova plataforma FTP Web Explorer!** 🎉
