// Estado da aplicação
let currentPath = '/';
let isConnected = false;
let navigationHistory = [];

// Elementos DOM
const connectionPanel = document.getElementById('connectionPanel');
const fileExplorer = document.getElementById('fileExplorer');
const uploadArea = document.getElementById('uploadArea');
const connectionStatus = document.getElementById('connectionStatus');
const ftpForm = document.getElementById('ftpForm');
const fileList = document.getElementById('fileList');
const breadcrumb = document.getElementById('breadcrumb');

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Form de conexão FTP
    ftpForm.addEventListener('submit', handleFTPConnection);
    
    // Botões de navegação
    document.getElementById('backBtn').addEventListener('click', goBack);
    document.getElementById('upBtn').addEventListener('click', goUp);
    document.getElementById('refreshBtn').addEventListener('click', refreshCurrentDirectory);
    document.getElementById('disconnectBtn').addEventListener('click', disconnect);
    
    // Botões de ação
    document.getElementById('newFolderBtn').addEventListener('click', () => showModal('newFolderModal'));
    document.getElementById('uploadBtn').addEventListener('click', () => toggleUploadArea());
    
    // Upload drag & drop
    setupUploadArea();
    
    // Verificar se já existe uma conexão ativa
    checkExistingConnection();
}

async function handleFTPConnection(e) {
    e.preventDefault();
    
    const formData = new FormData(ftpForm);
    const connectionData = {
        host: formData.get('host'),
        port: parseInt(formData.get('port')),
        username: formData.get('username'),
        password: formData.get('password')
    };
    
    try {
        showNotification('Conectando ao servidor FTP...', 'info');
        
        const response = await fetch('/api/connect', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(connectionData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            isConnected = true;
            updateConnectionStatus(true);
            showFileExplorer();
            loadDirectory('/');
            showNotification('Conectado com sucesso!', 'success');
        } else {
            showNotification('Erro na conexão: ' + result.error, 'error');
        }
    } catch (error) {
        showNotification('Erro de conexão: ' + error.message, 'error');
    }
}

async function loadDirectory(path) {
    try {
        const response = await fetch(`/api/list?path=${encodeURIComponent(path)}`, {
            credentials: 'include'
        });
        const result = await response.json();
        
        if (result.success) {
            currentPath = path;
            updateBreadcrumb(path);
            renderFileList(result.files);
            updateNavigationButtons();
        } else {
            showNotification('Erro ao carregar diretório: ' + result.error, 'error');
        }
    } catch (error) {
        showNotification('Erro ao carregar diretório: ' + error.message, 'error');
    }
}

function renderFileList(files) {
    fileList.innerHTML = '';
    
    files.forEach(file => {
        const fileItem = document.createElement('div');
        fileItem.className = `file-item ${file.type}`;
        
        fileItem.innerHTML = `
            <div class="file-name">
                <i class="fas ${getFileIcon(file)}"></i>
                <span>${file.name}</span>
            </div>
            <div class="file-size">${file.type === 'file' ? formatFileSize(file.size) : '-'}</div>
            <div class="file-date">${formatDate(file.date)}</div>
            <div class="file-actions">
                ${file.type === 'file' ? 
                    `<button class="btn btn-primary" onclick="downloadFile('${file.name}')">
                        <i class="fas fa-download"></i>
                    </button>` : ''
                }
                <button class="btn btn-secondary" onclick="renameItem('${file.name}', '${file.type}')">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-danger" onclick="deleteItem('${file.name}', '${file.type}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        
        // Adicionar evento de duplo clique para navegar
        if (file.type === 'directory') {
            fileItem.addEventListener('dblclick', () => {
                navigateToDirectory(file.name);
            });
        }
        
        fileList.appendChild(fileItem);
    });
}

function getFileIcon(file) {
    if (file.type === 'directory') {
        return 'fa-folder';
    }
    
    const extension = file.name.split('.').pop().toLowerCase();
    const iconMap = {
        'pdf': 'fa-file-pdf',
        'doc': 'fa-file-word',
        'docx': 'fa-file-word',
        'xls': 'fa-file-excel',
        'xlsx': 'fa-file-excel',
        'ppt': 'fa-file-powerpoint',
        'pptx': 'fa-file-powerpoint',
        'jpg': 'fa-file-image',
        'jpeg': 'fa-file-image',
        'png': 'fa-file-image',
        'gif': 'fa-file-image',
        'mp4': 'fa-file-video',
        'avi': 'fa-file-video',
        'mp3': 'fa-file-audio',
        'wav': 'fa-file-audio',
        'zip': 'fa-file-archive',
        'rar': 'fa-file-archive',
        'txt': 'fa-file-alt',
        'html': 'fa-file-code',
        'css': 'fa-file-code',
        'js': 'fa-file-code',
        'php': 'fa-file-code',
        'py': 'fa-file-code'
    };
    
    return iconMap[extension] || 'fa-file';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR') + ' ' + date.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

function navigateToDirectory(dirName) {
    navigationHistory.push(currentPath);
    const newPath = currentPath === '/' ? `/${dirName}` : `${currentPath}/${dirName}`;
    loadDirectory(newPath);
}

function goBack() {
    if (navigationHistory.length > 0) {
        const previousPath = navigationHistory.pop();
        loadDirectory(previousPath);
    }
}

function goUp() {
    if (currentPath !== '/') {
        const parentPath = currentPath.substring(0, currentPath.lastIndexOf('/')) || '/';
        navigationHistory.push(currentPath);
        loadDirectory(parentPath);
    }
}

function refreshCurrentDirectory() {
    loadDirectory(currentPath);
}

function updateBreadcrumb(path) {
    breadcrumb.innerHTML = '';
    
    const parts = path.split('/').filter(part => part !== '');
    
    // Root
    const rootItem = document.createElement('span');
    rootItem.className = 'breadcrumb-item';
    rootItem.textContent = '/';
    rootItem.addEventListener('click', () => loadDirectory('/'));
    breadcrumb.appendChild(rootItem);
    
    // Path parts
    let currentBreadcrumbPath = '';
    parts.forEach((part, index) => {
        currentBreadcrumbPath += '/' + part;
        const item = document.createElement('span');
        item.className = 'breadcrumb-item';
        if (index === parts.length - 1) {
            item.classList.add('active');
        }
        item.textContent = part;
        
        const pathToNavigate = currentBreadcrumbPath;
        item.addEventListener('click', () => loadDirectory(pathToNavigate));
        breadcrumb.appendChild(item);
    });
}

function updateNavigationButtons() {
    document.getElementById('backBtn').disabled = navigationHistory.length === 0;
    document.getElementById('upBtn').disabled = currentPath === '/';
}

function updateConnectionStatus(connected) {
    const indicator = connectionStatus.querySelector('.status-indicator');
    const text = connectionStatus.querySelector('.status-text');
    
    if (connected) {
        indicator.classList.add('connected');
        text.textContent = 'Conectado';
    } else {
        indicator.classList.remove('connected');
        text.textContent = 'Desconectado';
    }
}

function showFileExplorer() {
    connectionPanel.style.display = 'none';
    fileExplorer.style.display = 'block';
}

function hideFileExplorer() {
    fileExplorer.style.display = 'none';
    uploadArea.style.display = 'none';
    connectionPanel.style.display = 'block';
}

async function disconnect() {
    try {
        await fetch('/api/disconnect', {
            method: 'POST',
            credentials: 'include'
        });
        isConnected = false;
        updateConnectionStatus(false);
        hideFileExplorer();
        currentPath = '/';
        navigationHistory = [];
        showNotification('Desconectado com sucesso!', 'success');
    } catch (error) {
        showNotification('Erro ao desconectar: ' + error.message, 'error');
    }
}

async function checkExistingConnection() {
    try {
        const response = await fetch('/api/status', {
            credentials: 'include'
        });
        const result = await response.json();
        
        if (result.connected) {
            isConnected = true;
            updateConnectionStatus(true);
            showFileExplorer();
            loadDirectory('/');
        }
    } catch (error) {
        console.log('Nenhuma conexão ativa encontrada');
    }
}

// Utility functions
function showNotification(message, type = 'info') {
    const notifications = document.getElementById('notifications');
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    notifications.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

function showModal(modalId) {
    document.getElementById(modalId).style.display = 'flex';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function toggleUploadArea() {
    const isVisible = uploadArea.style.display !== 'none';
    uploadArea.style.display = isVisible ? 'none' : 'block';
}

// Upload functionality
function setupUploadArea() {
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('fileInput');

    dropZone.addEventListener('click', () => fileInput.click());

    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('dragover');
    });

    dropZone.addEventListener('dragleave', () => {
        dropZone.classList.remove('dragover');
    });

    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('dragover');
        handleFileUpload(e.dataTransfer.files);
    });

    fileInput.addEventListener('change', (e) => {
        handleFileUpload(e.target.files);
    });
}

async function handleFileUpload(files) {
    if (files.length === 0) return;

    const progressContainer = document.getElementById('uploadProgress');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');

    progressContainer.style.display = 'block';

    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const formData = new FormData();
        formData.append('file', file);
        formData.append('path', currentPath);

        try {
            showNotification(`Enviando ${file.name}...`, 'info');

            const response = await fetch('/api/upload', {
                method: 'POST',
                credentials: 'include',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                showNotification(`${file.name} enviado com sucesso!`, 'success');
            } else {
                showNotification(`Erro ao enviar ${file.name}: ${result.error}`, 'error');
            }

            // Update progress
            const progress = ((i + 1) / files.length) * 100;
            progressFill.style.width = progress + '%';
            progressText.textContent = Math.round(progress) + '%';

        } catch (error) {
            showNotification(`Erro ao enviar ${file.name}: ${error.message}`, 'error');
        }
    }

    // Refresh directory after upload
    setTimeout(() => {
        refreshCurrentDirectory();
        progressContainer.style.display = 'none';
        uploadArea.style.display = 'none';
    }, 1000);
}

// File operations
async function downloadFile(fileName) {
    try {
        showNotification(`Baixando ${fileName}...`, 'info');

        const response = await fetch(`/api/download?path=${encodeURIComponent(currentPath)}&file=${encodeURIComponent(fileName)}`, {
            credentials: 'include'
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            showNotification(`${fileName} baixado com sucesso!`, 'success');
        } else {
            const result = await response.json();
            showNotification(`Erro ao baixar ${fileName}: ${result.error}`, 'error');
        }
    } catch (error) {
        showNotification(`Erro ao baixar ${fileName}: ${error.message}`, 'error');
    }
}

async function createFolder() {
    const folderName = document.getElementById('folderNameInput').value.trim();

    if (!folderName) {
        showNotification('Nome da pasta é obrigatório', 'error');
        return;
    }

    try {
        const response = await fetch('/api/mkdir', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
                path: currentPath,
                name: folderName
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Pasta "${folderName}" criada com sucesso!`, 'success');
            refreshCurrentDirectory();
            closeModal('newFolderModal');
            document.getElementById('folderNameInput').value = '';
        } else {
            showNotification(`Erro ao criar pasta: ${result.error}`, 'error');
        }
    } catch (error) {
        showNotification(`Erro ao criar pasta: ${error.message}`, 'error');
    }
}

async function deleteItem(itemName, itemType) {
    const confirmModal = document.getElementById('confirmModal');
    const confirmTitle = document.getElementById('confirmTitle');
    const confirmMessage = document.getElementById('confirmMessage');
    const confirmBtn = document.getElementById('confirmBtn');

    confirmTitle.textContent = `Excluir ${itemType === 'directory' ? 'Pasta' : 'Arquivo'}`;
    confirmMessage.textContent = `Tem certeza que deseja excluir "${itemName}"?`;

    confirmBtn.onclick = async () => {
        try {
            const response = await fetch('/api/delete', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    path: currentPath,
                    name: itemName,
                    type: itemType
                })
            });

            const result = await response.json();

            if (result.success) {
                showNotification(`"${itemName}" excluído com sucesso!`, 'success');
                refreshCurrentDirectory();
            } else {
                showNotification(`Erro ao excluir "${itemName}": ${result.error}`, 'error');
            }

            closeModal('confirmModal');
        } catch (error) {
            showNotification(`Erro ao excluir "${itemName}": ${error.message}`, 'error');
            closeModal('confirmModal');
        }
    };

    showModal('confirmModal');
}

async function renameItem(oldName, itemType) {
    const newName = prompt(`Novo nome para "${oldName}":`);

    if (!newName || newName === oldName) {
        return;
    }

    try {
        const response = await fetch('/api/rename', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
                path: currentPath,
                oldName: oldName,
                newName: newName,
                type: itemType
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`"${oldName}" renomeado para "${newName}" com sucesso!`, 'success');
            refreshCurrentDirectory();
        } else {
            showNotification(`Erro ao renomear "${oldName}": ${result.error}`, 'error');
        }
    } catch (error) {
        showNotification(`Erro ao renomear "${oldName}": ${error.message}`, 'error');
    }
}
