const express = require('express');
const FTP = require('ftp');
const multer = require('multer');
const cors = require('cors');
const session = require('express-session');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors({
    origin: true,
    credentials: true
}));
app.use(express.json());
app.use(express.static('.'));

// Session configuration
app.use(session({
    secret: 'ftp-web-explorer-secret',
    resave: false,
    saveUninitialized: true,
    cookie: {
        secure: false,
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        httpOnly: false
    }
}));

// Multer configuration for file uploads
const upload = multer({
    dest: 'uploads/',
    limits: {
        fileSize: 100 * 1024 * 1024 // 100MB limit
    }
});

// FTP connection storage
let ftpConnections = new Map();

// Helper function to get FTP connection for session
function getFTPConnection(sessionId) {
    console.log('Getting FTP connection for session:', sessionId);
    console.log('Available connections:', Array.from(ftpConnections.keys()));
    const connection = ftpConnections.get(sessionId);
    console.log('Connection found:', !!connection);
    return connection;
}

// Helper function to create FTP connection
function createFTPConnection(sessionId, config) {
    return new Promise((resolve, reject) => {
        const ftp = new FTP();
        
        ftp.on('ready', () => {
            console.log('FTP connection ready for session:', sessionId);
            ftpConnections.set(sessionId, ftp);
            console.log('Connection stored. Total connections:', ftpConnections.size);
            resolve(ftp);
        });
        
        ftp.on('error', (err) => {
            reject(err);
        });
        
        ftp.connect({
            host: config.host,
            port: config.port || 21,
            user: config.username,
            password: config.password,
            connTimeout: 10000,
            pasvTimeout: 10000,
            keepalive: 10000
        });
    });
}

// Routes

// Connect to FTP server
app.post('/api/connect', async (req, res) => {
    try {
        const { host, port, username, password } = req.body;
        
        if (!host || !username || !password) {
            return res.json({ success: false, error: 'Todos os campos são obrigatórios' });
        }
        
        // Close existing connection if any
        const existingConnection = getFTPConnection(req.sessionID);
        if (existingConnection) {
            existingConnection.end();
            ftpConnections.delete(req.sessionID);
        }
        
        const ftp = await createFTPConnection(req.sessionID, {
            host,
            port: port || 21,
            username,
            password
        });
        
        res.json({ success: true, message: 'Conectado com sucesso' });
        
    } catch (error) {
        console.error('FTP Connection Error:', error);
        res.json({ success: false, error: error.message });
    }
});

// Check connection status
app.get('/api/status', (req, res) => {
    const ftp = getFTPConnection(req.sessionID);
    res.json({ connected: !!ftp });
});

// List directory contents
app.get('/api/list', (req, res) => {
    const ftp = getFTPConnection(req.sessionID);
    
    if (!ftp) {
        return res.json({ success: false, error: 'Não conectado ao FTP' });
    }
    
    const dirPath = req.query.path || '/';
    
    ftp.list(dirPath, (err, list) => {
        if (err) {
            console.error('List Error:', err);
            return res.json({ success: false, error: err.message });
        }
        
        const files = list.map(item => ({
            name: item.name,
            type: item.type === 'd' ? 'directory' : 'file',
            size: item.size || 0,
            date: item.date || new Date(),
            permissions: item.rights || {}
        }));
        
        res.json({ success: true, files });
    });
});

// Upload file
app.post('/api/upload', upload.single('file'), (req, res) => {
    const ftp = getFTPConnection(req.sessionID);
    
    if (!ftp) {
        return res.json({ success: false, error: 'Não conectado ao FTP' });
    }
    
    if (!req.file) {
        return res.json({ success: false, error: 'Nenhum arquivo enviado' });
    }
    
    const remotePath = req.body.path || '/';
    const remoteFilePath = path.posix.join(remotePath, req.file.originalname);
    
    ftp.put(req.file.path, remoteFilePath, (err) => {
        // Clean up uploaded file
        fs.unlink(req.file.path, () => {});
        
        if (err) {
            console.error('Upload Error:', err);
            return res.json({ success: false, error: err.message });
        }
        
        res.json({ success: true, message: 'Arquivo enviado com sucesso' });
    });
});

// Download file
app.get('/api/download', (req, res) => {
    const ftp = getFTPConnection(req.sessionID);
    
    if (!ftp) {
        return res.json({ success: false, error: 'Não conectado ao FTP' });
    }
    
    const filePath = req.query.path || '/';
    const fileName = req.query.file;
    
    if (!fileName) {
        return res.json({ success: false, error: 'Nome do arquivo é obrigatório' });
    }
    
    const remoteFilePath = path.posix.join(filePath, fileName);
    
    ftp.get(remoteFilePath, (err, stream) => {
        if (err) {
            console.error('Download Error:', err);
            return res.json({ success: false, error: err.message });
        }
        
        res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
        res.setHeader('Content-Type', 'application/octet-stream');
        
        stream.pipe(res);
        
        stream.on('error', (streamErr) => {
            console.error('Stream Error:', streamErr);
            if (!res.headersSent) {
                res.json({ success: false, error: streamErr.message });
            }
        });
    });
});

// Create directory
app.post('/api/mkdir', (req, res) => {
    const ftp = getFTPConnection(req.sessionID);
    
    if (!ftp) {
        return res.json({ success: false, error: 'Não conectado ao FTP' });
    }
    
    const { path: dirPath, name } = req.body;
    
    if (!name) {
        return res.json({ success: false, error: 'Nome da pasta é obrigatório' });
    }
    
    const remoteDirPath = path.posix.join(dirPath || '/', name);
    
    ftp.mkdir(remoteDirPath, (err) => {
        if (err) {
            console.error('Mkdir Error:', err);
            return res.json({ success: false, error: err.message });
        }
        
        res.json({ success: true, message: 'Pasta criada com sucesso' });
    });
});

// Delete file or directory
app.delete('/api/delete', (req, res) => {
    const ftp = getFTPConnection(req.sessionID);
    
    if (!ftp) {
        return res.json({ success: false, error: 'Não conectado ao FTP' });
    }
    
    const { path: itemPath, name, type } = req.body;
    
    if (!name) {
        return res.json({ success: false, error: 'Nome do item é obrigatório' });
    }
    
    const remoteItemPath = path.posix.join(itemPath || '/', name);
    
    if (type === 'directory') {
        ftp.rmdir(remoteItemPath, (err) => {
            if (err) {
                console.error('Rmdir Error:', err);
                return res.json({ success: false, error: err.message });
            }
            
            res.json({ success: true, message: 'Pasta excluída com sucesso' });
        });
    } else {
        ftp.delete(remoteItemPath, (err) => {
            if (err) {
                console.error('Delete Error:', err);
                return res.json({ success: false, error: err.message });
            }
            
            res.json({ success: true, message: 'Arquivo excluído com sucesso' });
        });
    }
});

// Rename file or directory
app.post('/api/rename', (req, res) => {
    const ftp = getFTPConnection(req.sessionID);
    
    if (!ftp) {
        return res.json({ success: false, error: 'Não conectado ao FTP' });
    }
    
    const { path: itemPath, oldName, newName } = req.body;
    
    if (!oldName || !newName) {
        return res.json({ success: false, error: 'Nomes antigo e novo são obrigatórios' });
    }
    
    const oldPath = path.posix.join(itemPath || '/', oldName);
    const newPath = path.posix.join(itemPath || '/', newName);
    
    ftp.rename(oldPath, newPath, (err) => {
        if (err) {
            console.error('Rename Error:', err);
            return res.json({ success: false, error: err.message });
        }
        
        res.json({ success: true, message: 'Item renomeado com sucesso' });
    });
});

// Disconnect from FTP
app.post('/api/disconnect', (req, res) => {
    const ftp = getFTPConnection(req.sessionID);
    
    if (ftp) {
        ftp.end();
        ftpConnections.delete(req.sessionID);
    }
    
    res.json({ success: true, message: 'Desconectado com sucesso' });
});

// Serve the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Server Error:', err);
    res.status(500).json({ success: false, error: 'Erro interno do servidor' });
});

// Clean up connections on server shutdown
process.on('SIGINT', () => {
    console.log('Fechando conexões FTP...');
    ftpConnections.forEach((ftp) => {
        ftp.end();
    });
    ftpConnections.clear();
    process.exit(0);
});

// Start server
app.listen(PORT, () => {
    console.log(`Servidor FTP Web Explorer rodando na porta ${PORT}`);
    console.log(`Acesse: http://localhost:${PORT}`);
    console.log('Servidor iniciado com sucesso!');
});

module.exports = app;
